
import React, { useState } from "react";
import { MessageCircle, Phone, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const WhatsAppButton = () => {
  const phoneNumber = "917449423849";
  const message = "Hi! I need help with cyber services. Can you assist me?";

  // Properly encode the message for URL compatibility
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

  return (
    <a href={whatsappUrl} target="_blank" rel="noopener noreferrer" className="fixed bottom-6 right-6 z-50 group">
      {/* Main Button */}
      <Button
        className="relative bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full w-16 h-16 shadow-2xl hover:shadow-green-500/25 transition-all duration-300 hover:scale-110 border-2 border-white/20 backdrop-blur-sm"
        size="lg"
      >         
      <MessageCircle className="h-8 w-8 group-hover:scale-110 transition-transform duration-300" />
        {/* Glowing Effect */}
        <div className="absolute inset-0 bg-green-500 rounded-full blur-lg opacity-50 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
      </Button>
      
      {/* Animated Rings */}
      <div className="absolute inset-0 rounded-full">
        <div className="absolute inset-0 bg-green-500/30 rounded-full animate-ping"></div>
        <div className="absolute inset-0 bg-green-500/20 rounded-full animate-ping delay-300"></div>
        <div className="absolute inset-0 bg-green-500/10 rounded-full animate-ping delay-700"></div>
      </div>
      
      {/* Tooltip */}
      <div className="absolute bottom-full right-0 mb-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
        <div className="bg-white/90 backdrop-blur-md text-gray-800 px-4 py-2 rounded-lg shadow-xl border border-white/20 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-green-600" />
            <div>
              <div>WhatsApp Us Now!</div>
              <div className="text-xs text-gray-600">+91 74494 23849</div>
            </div>
          </div>
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white/90"></div>
        </div>
      </div>

      {/* Floating Notification Badge */}
      <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center animate-bounce shadow-lg">
        !
      </div>
    </a>
  );
};

export default WhatsAppButton;
