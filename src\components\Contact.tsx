
import { MapPin, Phone, Clock, Mail, Navigation } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";

const Contact = () => {
  const { t } = useLanguage();
  
  return (
    <section id="contact" className="py-20 px-4 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            {t('contactTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('contactSubtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <MapPin className="h-8 w-8 text-blue-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('ourAddress')}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {t('addressLine1')}<br />
                      {t('addressLine2')}<br />
                      {t('addressLine3')}<br />
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Phone className="h-8 w-8 text-green-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('phoneWhatsapp')}</h3>
                    <p className="text-gray-600 mb-2">
                      <strong>{t('callLabel')}</strong>
                      <a href="tel:+917449423849" className="text-blue-600 hover:underline ml-1">
                        +91 74494 23849
                      </a><br />
                      <strong>{t('whatsappLabel')}</strong>
                      <a href="https://wa.me/917449423849" target="_blank" rel="noopener noreferrer" className="text-green-600 hover:underline ml-1">
                        +91 74494 23849
                      </a>
                    </p>
                    <Button
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => window.open('https://wa.me/917449423849?text=Hi! I need help with cyber services. Can you assist me?', '_blank')}
                    >
                      {t('whatsappNow')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Clock className="h-8 w-8 text-purple-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('shopTimings')}</h3>
                    <div className="text-gray-600">
                      <p><strong>{t('weekdays')}</strong> {t('weekdayHours')}</p>
                      <p><strong>{t('sunday')}</strong> {t('sundayHours')}</p>
                      <p className="text-sm text-green-600 mt-2">{t('emergencyServices')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-8 w-8 text-blue-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('emailUs')}</h3>
                    <p className="text-gray-600">
                      <EMAIL><br />
                      <span className="text-sm text-gray-500">{t('emailResponse')}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Map and Quick Actions */}
          <div className="space-y-6">
            {/* Map Placeholder */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <Navigation className="h-6 w-6 text-blue-600 mr-2" />
                  {t('findUsOnMap')}
                </h3>
                <div className="rounded-lg overflow-hidden shadow-md">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3677.9830836962597!2d88.3462475!3d22.8030907!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39f891d4288909f1%3A0xbc9915b5f5fb7018!2sGraphic%20%26%20Cyber%20Support!5e0!3m2!1sen!2sin!4v1748791447153!5m2!1sen!2sin"
                    width="100%"
                    height="180"
                    style={{ border: 0 }}
                    allowFullScreen={true}
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Graphic & Cyber Support Location"
                  />
                </div>
                <Button
                  className="w-full mt-4 bg-blue-600 hover:bg-blue-700"
                  onClick={() => window.open('https://maps.google.com/?q=Graphic+%26+Cyber+Support,+Kolkata', '_blank')}
                >
                  {t('openInGoogleMaps')}
                </Button>
              </CardContent>
            </Card>

            {/* Quick Contact Form */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">{t('quickEnquiry')}</h3>
                <div className="space-y-4">
                  <input 
                    type="text" 
                    placeholder={t('yourName')}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input 
                    type="tel" 
                    placeholder={t('phoneNumber')}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <textarea 
                    placeholder={t('whatServiceNeed')}
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => {
                      // Get form values
                      const name = (document.querySelector('input[placeholder*="Name"]') as HTMLInputElement)?.value || '';
                      const phone = (document.querySelector('input[type="tel"]') as HTMLInputElement)?.value || '';
                      const service = (document.querySelector('textarea') as HTMLTextAreaElement)?.value || '';

                      // Create WhatsApp message
                      const message = `Hi! I'm ${name}. Phone: ${phone}. I need help with: ${service}`;
                      const whatsappUrl = `https://wa.me/917449423849?text=${encodeURIComponent(message)}`;

                      // Open WhatsApp
                      window.open(whatsappUrl, '_blank');
                    }}
                  >
                    {t('sendEnquiry')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Important Notice */}
        <div className="mt-12 text-center">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h4 className="text-lg font-bold text-yellow-800 mb-2">{t('importantNotice')}</h4>
            <p className="text-yellow-700">
              {t('covidSafety')}<br />
              {t('digitalPayment')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
